#!/usr/bin/php
<?php
/**
 * Script per IMPORTAZIONE studenti, parenti e relazioni nel database di destinazione
 *
 * PARTE 2: Da eseguire sul SERVER A (destinazione)
 *
 * Importa nel database A i dati esportati dal database B:
 * - studenti (con nuovi id_studente)
 * - parenti (con nuovi id_parente)
 * - parenti_studenti (adeguato ai nuovi ID)
 * - storia_studenti (adeguato ai nuovi id_studente)
 * - classi_studenti (adeguato ai nuovi id_studente e id_classe)
 *
 * Gestisce duplicati per codice fiscale e mapping delle classi
 *
 * PREREQUISITI:
 * - Aver eseguito lo script di esportazione sul server B
 * - Aver copiato i file .copy nella directory /tmp/ di questo server
 */

require __DIR__ . '/../configs/mastercom.php';
require MC_PATH . '/functions/common.php';
require MC_PATH . '/common/dbconnect.php';

// Configurazione database locale (SERVER A - destinazione)
$config = [
    'db_a' => [
        'name' => 'mastercom_2025_2026',
        'host' => 'localhost',
        'port' => 5432,
        'user' => 'postgres',
        'password' => 'postgres'
    ]
];

// Directory dei file esportati
$export_dir = '/tmp/';

// Verifica esistenza file necessari
$required_files = [
    'studenti_export.copy',
    'parenti_export.copy',
    'parenti_studenti_export.copy',
    'storia_studenti_export.copy',
    'classi_studenti_export.copy'
];

echo "=== VERIFICA FILE DI ESPORTAZIONE ===\n";
foreach ($required_files as $file) {
    if (!file_exists($export_dir . $file)) {
        die("ERRORE: File mancante: {$export_dir}{$file}\nEseguire prima lo script di esportazione sul server B e copiare i file.\n");
    }
    echo "✓ Trovato: {$file}\n";
}

// Connessione database locale
$conn = pg_connect("host={$config['db_a']['host']} port={$config['db_a']['port']} dbname={$config['db_a']['name']} user={$config['db_a']['user']} password={$config['db_a']['password']}");

if (!$conn) {
    die("Errore di connessione al database locale\n");
}

// Array per il report
$report = [
    'studenti_importati' => [],
    'studenti_non_importati' => [],
    'parenti_importati' => [],
    'parenti_non_importati' => [],
    'parenti_esistenti_riutilizzati' => [],
    'relazioni_create' => [],
    'storia_importata' => [],
    'classi_studenti_importate' => [],
    'errori' => []
];

// Mapping per ID
$mapping_studenti = [];
$mapping_parenti = [];
$mapping_classi = [];

echo "=== INIZIO IMPORTAZIONE ===\n";

try {
    // 1. CARICAMENTO DATI DA FILE E MAPPING CLASSI
    echo "1. Caricamento mapping classi da file...\n";

    // Carica classi del database A (destinazione)
    $query_classi_a = "SELECT id_classe, classe, sezione FROM classi WHERE flag_canc = 0";
    $result_classi_a = pg_query($conn, $query_classi_a);
    $classi_a = [];
    while ($row = pg_fetch_assoc($result_classi_a)) {
        $key = $row['classe'] . '_' . $row['sezione'];
        $classi_a[$key] = $row['id_classe'];
    }

    // Crea tabella temporanea per classi del database B
    pg_query($conn, "DROP TABLE IF EXISTS classi_mapping_temp");
    pg_query($conn, "CREATE TEMPORARY TABLE classi_mapping_temp (id_classe integer, classe varchar, sezione varchar)");

    // Carica mapping classi da file
    $copy_result = pg_query($conn, "COPY classi_mapping_temp FROM '{$export_dir}classi_mapping_export.copy'");
    if (!$copy_result) {
        die("Errore caricamento mapping classi: " . pg_last_error($conn) . "\n");
    }

    // Crea mapping tra classi B e A
    $query_mapping = "SELECT id_classe, classe, sezione FROM classi_mapping_temp";
    $result_mapping = pg_query($conn, $query_mapping);
    while ($row = pg_fetch_assoc($result_mapping)) {
        $key = $row['classe'] . '_' . $row['sezione'];
        if (isset($classi_a[$key])) {
            $mapping_classi[$row['id_classe']] = $classi_a[$key];
        } else {
            $report['errori'][] = "Classe non trovata in DB A: {$row['classe']} {$row['sezione']}";
        }
    }

    echo "Mapping classi completato: " . count($mapping_classi) . " classi mappate\n";

    // 2. ANALISI STUDENTI ESISTENTI IN DB A
    echo "2. Analisi studenti esistenti in DB A...\n";

    $query_studenti_a = "SELECT codice_fiscale FROM studenti WHERE flag_canc = 0 AND codice_fiscale != ''";
    $result_studenti_a = pg_query($conn, $query_studenti_a);
    $studenti_esistenti_a = [];
    while ($row = pg_fetch_assoc($result_studenti_a)) {
        $studenti_esistenti_a[strtoupper(trim($row['codice_fiscale']))] = true;
    }

    echo "Trovati " . count($studenti_esistenti_a) . " studenti esistenti in DB A\n";

    // 3. ANALISI PARENTI ESISTENTI IN DB A
    echo "3. Analisi parenti esistenti in DB A...\n";

    $query_parenti_a = "SELECT id_parente, codice_fiscale FROM parenti WHERE flag_canc = 0 AND codice_fiscale != ''";
    $result_parenti_a = pg_query($conn, $query_parenti_a);
    $parenti_esistenti_a = [];
    while ($row = pg_fetch_assoc($result_parenti_a)) {
        $parenti_esistenti_a[strtoupper(trim($row['codice_fiscale']))] = $row['id_parente'];
    }

    echo "Trovati " . count($parenti_esistenti_a) . " parenti esistenti in DB A\n";

    // 4. CARICAMENTO E IMPORTAZIONE STUDENTI
    echo "4. Caricamento e importazione studenti...\n";

    // Crea tabella temporanea per studenti
    pg_query($conn, "DROP TABLE IF EXISTS studenti_temp");
    $create_temp = "CREATE TEMPORARY TABLE studenti_temp AS (SELECT * FROM studenti) WITH NO DATA";
    pg_query($conn, $create_temp);

    // Carica studenti da file
    $copy_result = pg_query($conn, "COPY studenti_temp FROM '{$export_dir}studenti_export.copy'");
    if (!$copy_result) {
        die("Errore caricamento studenti: " . pg_last_error($conn) . "\n");
    }

    // Processa studenti dalla tabella temporanea
    $query_studenti_temp = "SELECT * FROM studenti_temp ORDER BY id_studente";
    $result_studenti_temp = pg_query($conn, $query_studenti_temp);

    while ($studente = pg_fetch_assoc($result_studenti_temp)) {
        $cf_studente = strtoupper(trim($studente['codice_fiscale']));

        if ($cf_studente && isset($studenti_esistenti_a[$cf_studente])) {
            $report['studenti_non_importati'][] = [
                'id_originale' => $studente['id_studente'],
                'nome' => $studente['nome'],
                'cognome' => $studente['cognome'],
                'codice_fiscale' => $cf_studente,
                'motivo' => 'Codice fiscale già esistente in DB A'
            ];
            continue;
        }

        // Genera nuovo ID studente
        $query_new_id = "SELECT nextval('studenti_id_studente_seq')";
        $result_new_id = pg_query($conn, $query_new_id);
        $new_id = pg_fetch_result($result_new_id, 0, 0);

        // Prepara i campi per l'inserimento (escludendo id_studente)
        $campi = [];
        $valori = [];

        foreach ($studente as $campo => $valore) {
            if ($campo != 'id_studente') {
                $campi[] = $campo;
                $valori[] = $valore === null ? 'NULL' : "'" . pg_escape_string($conn, $valore) . "'";
            }
        }

        $campi[] = 'id_studente';
        $valori[] = $new_id;

        $query_insert = "INSERT INTO studenti (" . implode(', ', $campi) . ") VALUES (" . implode(', ', $valori) . ")";

        if (pg_query($conn, $query_insert)) {
            $mapping_studenti[$studente['id_studente']] = $new_id;
            $report['studenti_importati'][] = [
                'id_originale' => $studente['id_studente'],
                'id_nuovo' => $new_id,
                'nome' => $studente['nome'],
                'cognome' => $studente['cognome'],
                'codice_fiscale' => $cf_studente
            ];
        } else {
            $report['errori'][] = "Errore inserimento studente ID {$studente['id_studente']}: " . pg_last_error($conn);
        }
    }

    echo "Studenti importati: " . count($report['studenti_importati']) . "\n";
    echo "Studenti non importati: " . count($report['studenti_non_importati']) . "\n";

    // 5. CARICAMENTO E IMPORTAZIONE PARENTI
    echo "5. Caricamento e importazione parenti...\n";

    // Crea tabella temporanea per parenti
    pg_query($conn, "DROP TABLE IF EXISTS parenti_temp");
    $create_temp = "CREATE TEMPORARY TABLE parenti_temp AS (SELECT * FROM parenti) WITH NO DATA";
    pg_query($conn, $create_temp);

    // Carica parenti da file
    $copy_result = pg_query($conn, "COPY parenti_temp FROM '{$export_dir}parenti_export.copy'");
    if (!$copy_result) {
        die("Errore caricamento parenti: " . pg_last_error($conn) . "\n");
    }

    // Processa parenti dalla tabella temporanea
    $query_parenti_temp = "SELECT * FROM parenti_temp ORDER BY id_parente";
    $result_parenti_temp = pg_query($conn, $query_parenti_temp);

    while ($parente = pg_fetch_assoc($result_parenti_temp)) {
        $cf_parente = strtoupper(trim($parente['codice_fiscale']));

        if ($cf_parente && isset($parenti_esistenti_a[$cf_parente])) {
            // Parente già esistente, riutilizza l'ID esistente
            $mapping_parenti[$parente['id_parente']] = $parenti_esistenti_a[$cf_parente];
            $report['parenti_esistenti_riutilizzati'][] = [
                'id_originale' => $parente['id_parente'],
                'id_esistente' => $parenti_esistenti_a[$cf_parente],
                'nome' => $parente['nome'],
                'cognome' => $parente['cognome'],
                'codice_fiscale' => $cf_parente
            ];
            continue;
        }

        // Genera nuovo ID parente
        $query_new_id = "SELECT nextval('parenti_id_parente_seq')";
        $result_new_id = pg_query($conn, $query_new_id);
        $new_id = pg_fetch_result($result_new_id, 0, 0);

        // Prepara i campi per l'inserimento (escludendo id_parente)
        $campi = [];
        $valori = [];

        foreach ($parente as $campo => $valore) {
            if ($campo != 'id_parente') {
                $campi[] = $campo;
                $valori[] = $valore === null ? 'NULL' : "'" . pg_escape_string($conn, $valore) . "'";
            }
        }

        $campi[] = 'id_parente';
        $valori[] = $new_id;

        $query_insert = "INSERT INTO parenti (" . implode(', ', $campi) . ") VALUES (" . implode(', ', $valori) . ")";

        if (pg_query($conn, $query_insert)) {
            $mapping_parenti[$parente['id_parente']] = $new_id;
            $report['parenti_importati'][] = [
                'id_originale' => $parente['id_parente'],
                'id_nuovo' => $new_id,
                'nome' => $parente['nome'],
                'cognome' => $parente['cognome'],
                'codice_fiscale' => $cf_parente
            ];
        } else {
            $report['errori'][] = "Errore inserimento parente ID {$parente['id_parente']}: " . pg_last_error($conn);
        }
    }

    echo "Parenti importati: " . count($report['parenti_importati']) . "\n";
    echo "Parenti esistenti riutilizzati: " . count($report['parenti_esistenti_riutilizzati']) . "\n";

    // 6. CARICAMENTO E IMPORTAZIONE RELAZIONI PARENTI_STUDENTI
    echo "6. Caricamento e importazione relazioni parenti_studenti...\n";

    // Crea tabella temporanea per relazioni
    pg_query($conn, "DROP TABLE IF EXISTS parenti_studenti_temp");
    $create_temp = "CREATE TEMPORARY TABLE parenti_studenti_temp AS (SELECT * FROM parenti_studenti) WITH NO DATA";
    pg_query($conn, $create_temp);

    // Carica relazioni da file
    $copy_result = pg_query($conn, "COPY parenti_studenti_temp FROM '{$export_dir}parenti_studenti_export.copy'");
    if (!$copy_result) {
        die("Errore caricamento parenti_studenti: " . pg_last_error($conn) . "\n");
    }

    // Processa relazioni dalla tabella temporanea
    $query_rel_temp = "SELECT * FROM parenti_studenti_temp ORDER BY id_studente, id_parente";
    $result_rel_temp = pg_query($conn, $query_rel_temp);

    while ($relazione = pg_fetch_assoc($result_rel_temp)) {
        // Verifica che entrambi gli ID siano stati mappati
        if (!isset($mapping_studenti[$relazione['id_studente']]) || !isset($mapping_parenti[$relazione['id_parente']])) {
            $report['errori'][] = "Relazione saltata - studente o parente non mappato: studente {$relazione['id_studente']}, parente {$relazione['id_parente']}";
            continue;
        }

        $nuovo_id_studente = $mapping_studenti[$relazione['id_studente']];
        $nuovo_id_parente = $mapping_parenti[$relazione['id_parente']];

        // Prepara i campi per l'inserimento con i nuovi ID
        $campi = [];
        $valori = [];

        foreach ($relazione as $campo => $valore) {
            if ($campo == 'id_studente') {
                $campi[] = $campo;
                $valori[] = $nuovo_id_studente;
            } elseif ($campo == 'id_parente') {
                $campi[] = $campo;
                $valori[] = $nuovo_id_parente;
            } else {
                $campi[] = $campo;
                $valori[] = $valore === null ? 'NULL' : "'" . pg_escape_string($conn, $valore) . "'";
            }
        }

        $query_insert = "INSERT INTO parenti_studenti (" . implode(', ', $campi) . ") VALUES (" . implode(', ', $valori) . ")";

        if (pg_query($conn, $query_insert)) {
            $report['relazioni_create'][] = [
                'id_studente_originale' => $relazione['id_studente'],
                'id_parente_originale' => $relazione['id_parente'],
                'id_studente_nuovo' => $nuovo_id_studente,
                'id_parente_nuovo' => $nuovo_id_parente,
                'parentela' => $relazione['parentela']
            ];
        } else {
            $report['errori'][] = "Errore inserimento relazione studente {$relazione['id_studente']}, parente {$relazione['id_parente']}: " . pg_last_error($conn);
        }
    }

    echo "Relazioni parenti_studenti create: " . count($report['relazioni_create']) . "\n";

    // 7. CARICAMENTO E IMPORTAZIONE STORIA_STUDENTI
    echo "7. Caricamento e importazione storia_studenti...\n";

    // Crea tabella temporanea per storia_studenti
    pg_query($conn, "DROP TABLE IF EXISTS storia_studenti_temp");
    $create_temp = "CREATE TEMPORARY TABLE storia_studenti_temp AS (SELECT * FROM storia_studenti) WITH NO DATA";
    pg_query($conn, $create_temp);

    // Carica storia_studenti da file
    $copy_result = pg_query($conn, "COPY storia_studenti_temp FROM '{$export_dir}storia_studenti_export.copy'");
    if (!$copy_result) {
        die("Errore caricamento storia_studenti: " . pg_last_error($conn) . "\n");
    }

    // Processa storia_studenti dalla tabella temporanea
    $query_storia_temp = "SELECT * FROM storia_studenti_temp ORDER BY id_studente";
    $result_storia_temp = pg_query($conn, $query_storia_temp);

    while ($storia = pg_fetch_assoc($result_storia_temp)) {
        // Verifica che lo studente sia stato mappato
        if (!isset($mapping_studenti[$storia['id_studente']])) {
            $report['errori'][] = "Storia saltata - studente non mappato: studente {$storia['id_studente']}";
            continue;
        }

        $nuovo_id_studente = $mapping_studenti[$storia['id_studente']];

        // Genera nuovo ID storia_studenti
        $query_new_id = "SELECT nextval('storia_studenti_id_storia_studenti_seq')";
        $result_new_id = pg_query($conn, $query_new_id);
        $new_id = pg_fetch_result($result_new_id, 0, 0);

        // Prepara i campi per l'inserimento
        $campi = [];
        $valori = [];

        foreach ($storia as $campo => $valore) {
            if ($campo == 'id_storia_studenti') {
                $campi[] = $campo;
                $valori[] = $new_id;
            } elseif ($campo == 'id_studente') {
                $campi[] = $campo;
                $valori[] = $nuovo_id_studente;
            } elseif ($campo == 'id_classe' && $valore && isset($mapping_classi[$valore])) {
                // Mappa anche id_classe se presente e mappabile
                $campi[] = $campo;
                $valori[] = $mapping_classi[$valore];
            } else {
                $campi[] = $campo;
                $valori[] = $valore === null ? 'NULL' : "'" . pg_escape_string($conn, $valore) . "'";
            }
        }

        $query_insert = "INSERT INTO storia_studenti (" . implode(', ', $campi) . ") VALUES (" . implode(', ', $valori) . ")";

        if (pg_query($conn, $query_insert)) {
            $report['storia_importata'][] = [
                'id_originale' => $storia['id_storia_studenti'],
                'id_nuovo' => $new_id,
                'id_studente_originale' => $storia['id_studente'],
                'id_studente_nuovo' => $nuovo_id_studente,
                'anno_scolastico' => $storia['anno_scolastico']
            ];
        } else {
            $report['errori'][] = "Errore inserimento storia studente ID {$storia['id_studente']}: " . pg_last_error($conn);
        }
    }

    echo "Record storia_studenti importati: " . count($report['storia_importata']) . "\n";

    // 8. CARICAMENTO E IMPORTAZIONE CLASSI_STUDENTI
    echo "8. Caricamento e importazione classi_studenti...\n";

    // Crea tabella temporanea per classi_studenti
    pg_query($conn, "DROP TABLE IF EXISTS classi_studenti_temp");
    $create_temp = "CREATE TEMPORARY TABLE classi_studenti_temp AS (SELECT * FROM classi_studenti) WITH NO DATA";
    pg_query($conn, $create_temp);

    // Carica classi_studenti da file
    $copy_result = pg_query($conn, "COPY classi_studenti_temp FROM '{$export_dir}classi_studenti_export.copy'");
    if (!$copy_result) {
        die("Errore caricamento classi_studenti: " . pg_last_error($conn) . "\n");
    }

    // Processa classi_studenti dalla tabella temporanea
    $query_classi_stud_temp = "SELECT * FROM classi_studenti_temp ORDER BY id_studente, id_classe";
    $result_classi_stud_temp = pg_query($conn, $query_classi_stud_temp);

    while ($classe_stud = pg_fetch_assoc($result_classi_stud_temp)) {
        // Verifica che lo studente sia stato mappato
        if (!isset($mapping_studenti[$classe_stud['id_studente']])) {
            $report['errori'][] = "Classe_studenti saltata - studente non mappato: studente {$classe_stud['id_studente']}";
            continue;
        }

        // Verifica che la classe sia stata mappata
        if (!isset($mapping_classi[$classe_stud['id_classe']])) {
            $report['errori'][] = "Classe_studenti saltata - classe non mappata: classe {$classe_stud['id_classe']}";
            continue;
        }

        $nuovo_id_studente = $mapping_studenti[$classe_stud['id_studente']];
        $nuovo_id_classe = $mapping_classi[$classe_stud['id_classe']];

        // Prepara i campi per l'inserimento
        $campi = [];
        $valori = [];

        foreach ($classe_stud as $campo => $valore) {
            if ($campo == 'id_studente') {
                $campi[] = $campo;
                $valori[] = $nuovo_id_studente;
            } elseif ($campo == 'id_classe') {
                $campi[] = $campo;
                $valori[] = $nuovo_id_classe;
            } else {
                $campi[] = $campo;
                $valori[] = $valore === null ? 'NULL' : "'" . pg_escape_string($conn, $valore) . "'";
            }
        }

        $query_insert = "INSERT INTO classi_studenti (" . implode(', ', $campi) . ") VALUES (" . implode(', ', $valori) . ")";

        if (pg_query($conn, $query_insert)) {
            $report['classi_studenti_importate'][] = [
                'id_studente_originale' => $classe_stud['id_studente'],
                'id_classe_originale' => $classe_stud['id_classe'],
                'id_studente_nuovo' => $nuovo_id_studente,
                'id_classe_nuovo' => $nuovo_id_classe
            ];
        } else {
            $report['errori'][] = "Errore inserimento classe_studenti studente {$classe_stud['id_studente']}, classe {$classe_stud['id_classe']}: " . pg_last_error($conn);
        }
    }

    echo "Record classi_studenti importati: " . count($report['classi_studenti_importate']) . "\n";

} catch (Exception $e) {
    $report['errori'][] = "Errore generale: " . $e->getMessage();
    echo "ERRORE: " . $e->getMessage() . "\n";
}

// Chiusura connessione
pg_close($conn);

echo "=== FINE IMPORTAZIONE ===\n";
echo "Generazione report...\n";

// Genera report finale
$report_file = '/tmp/report_importazione_' . date('Y-m-d_H-i-s') . '.json';
file_put_contents($report_file, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "Report salvato in: $report_file\n";
echo "\n=== RIEPILOGO FINALE ===\n";
echo "- Studenti importati: " . count($report['studenti_importati']) . "\n";
echo "- Studenti non importati: " . count($report['studenti_non_importati']) . "\n";
echo "- Parenti importati: " . count($report['parenti_importati']) . "\n";
echo "- Parenti esistenti riutilizzati: " . count($report['parenti_esistenti_riutilizzati']) . "\n";
echo "- Relazioni parenti_studenti create: " . count($report['relazioni_create']) . "\n";
echo "- Record storia_studenti importati: " . count($report['storia_importata']) . "\n";
echo "- Record classi_studenti importati: " . count($report['classi_studenti_importate']) . "\n";
echo "- Errori totali: " . count($report['errori']) . "\n";

if (count($report['errori']) > 0) {
    echo "\n=== ERRORI RILEVATI ===\n";
    foreach ($report['errori'] as $errore) {
        echo "- $errore\n";
    }
}

echo "\n=== ANALISI AGGIUNTIVE NECESSARIE ===\n";
echo "Verifica se ci sono altre tabelle che referenziano id_studente, id_parente o id_classe:\n";
echo "- Tabelle con voti/valutazioni\n";
echo "- Tabelle con assenze\n";
echo "- Tabelle con note disciplinari\n";
echo "- Tabelle con comunicazioni\n";
echo "- Tabelle con pagamenti\n";
echo "- Altre tabelle specifiche del sistema\n";

// Genera anche un report in formato CSV per Excel
$csv_file = '/tmp/report_importazione_' . date('Y-m-d_H-i-s') . '.csv';
$csv_content = "Tipo,ID_Originale,ID_Nuovo,Nome,Cognome,Codice_Fiscale,Note\n";

foreach ($report['studenti_importati'] as $studente) {
    $csv_content .= "STUDENTE_IMPORTATO,{$studente['id_originale']},{$studente['id_nuovo']},{$studente['nome']},{$studente['cognome']},{$studente['codice_fiscale']},\n";
}

foreach ($report['studenti_non_importati'] as $studente) {
    $csv_content .= "STUDENTE_NON_IMPORTATO,{$studente['id_originale']},,{$studente['nome']},{$studente['cognome']},{$studente['codice_fiscale']},{$studente['motivo']}\n";
}

foreach ($report['parenti_importati'] as $parente) {
    $csv_content .= "PARENTE_IMPORTATO,{$parente['id_originale']},{$parente['id_nuovo']},{$parente['nome']},{$parente['cognome']},{$parente['codice_fiscale']},\n";
}

foreach ($report['parenti_esistenti_riutilizzati'] as $parente) {
    $csv_content .= "PARENTE_RIUTILIZZATO,{$parente['id_originale']},{$parente['id_esistente']},{$parente['nome']},{$parente['cognome']},{$parente['codice_fiscale']},Già esistente\n";
}

file_put_contents($csv_file, $csv_content);
echo "\nReport CSV salvato in: $csv_file\n";

?>
