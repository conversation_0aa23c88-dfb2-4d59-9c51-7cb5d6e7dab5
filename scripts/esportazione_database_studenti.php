#!/usr/bin/php
<?php
/**
 * Script per ESPORTAZIONE studenti, parenti e relazioni dal database di origine
 *
 * PARTE 1: Da eseguire sul SERVER B (origine)
 *
 * Esporta dal database B i seguenti dati in file .copy:
 * - studenti
 * - parenti
 * - parenti_studenti
 * - storia_studenti
 * - classi_studenti
 * - classi (per mapping)
 *
 * I file generati dovranno essere copiati sul server A per l'importazione
 */

require __DIR__ . '/../configs/mastercom.php';
require MC_PATH . '/functions/common.php';
require MC_PATH . '/common/dbconnect.php';

// Configurazione database locale (SERVER B - origine)
$config = [
    'db_b' => [
        'name' => 'mastercom_2025_2026',
        'host' => 'localhost',
        'port' => 5432,
        'user' => 'postgres',
        'password' => 'postgres'
    ]
];

// Directory di esportazione
$export_dir = '/tmp/';

// Connessione database locale
$conn = pg_connect("host={$config['db_b']['host']} port={$config['db_b']['port']} dbname={$config['db_b']['name']} user={$config['db_b']['user']} password={$config['db_b']['password']}");

if (!$conn) {
    die("Errore di connessione al database locale\n");
}

echo "=== ESPORTAZIONE DATI DAL SERVER B ===\n";

// Statistiche pre-esportazione
echo "\n1. STATISTICHE PRE-ESPORTAZIONE\n";
echo "=" . str_repeat("=", 50) . "\n";

$tables_stats = [
    'studenti' => 'SELECT COUNT(*) FROM studenti WHERE flag_canc = 0',
    'parenti' => 'SELECT COUNT(*) FROM parenti WHERE flag_canc = 0',
    'parenti_studenti' => 'SELECT COUNT(*) FROM parenti_studenti WHERE flag_canc = 0',
    'storia_studenti' => 'SELECT COUNT(*) FROM storia_studenti WHERE flag_canc = 0',
    'classi_studenti' => 'SELECT COUNT(*) FROM classi_studenti WHERE flag_canc = 0',
    'classi' => 'SELECT COUNT(*) FROM classi WHERE flag_canc = 0'
];

foreach ($tables_stats as $table => $query) {
    $result = pg_query($conn, $query);
    $count = pg_fetch_result($result, 0, 0);
    echo "- $table: $count record\n";
}

echo "\n2. ESPORTAZIONE STUDENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_studenti = "
COPY (
    SELECT *
    FROM studenti
    WHERE flag_canc = 0
) TO '{$export_dir}studenti_export.copy' WITH (FORMAT CSV, NULL '', DELIMITER E'\\t')
";

if (pg_query($conn, $query_studenti)) {
    echo "✓ Studenti esportati in studenti_export.copy\n";
} else {
    die("✗ Errore esportazione studenti: " . pg_last_error($conn) . "\n");
}

echo "\n3. ESPORTAZIONE PARENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_parenti = "
COPY (
    SELECT *
    FROM parenti
    WHERE flag_canc = 0
) TO '{$export_dir}parenti_export.copy' WITH (FORMAT CSV, NULL '', DELIMITER E'\\t')
";

if (pg_query($conn, $query_parenti)) {
    echo "✓ Parenti esportati in parenti_export.copy\n";
} else {
    die("✗ Errore esportazione parenti: " . pg_last_error($conn) . "\n");
}

echo "\n4. ESPORTAZIONE RELAZIONI PARENTI_STUDENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_parenti_studenti = "
COPY (
    SELECT *
    FROM parenti_studenti
    WHERE flag_canc = 0
) TO '{$export_dir}parenti_studenti_export.copy' WITH (FORMAT CSV, NULL '', DELIMITER E'\\t')
";

if (pg_query($conn, $query_parenti_studenti)) {
    echo "✓ Relazioni parenti_studenti esportate in parenti_studenti_export.copy\n";
} else {
    die("✗ Errore esportazione parenti_studenti: " . pg_last_error($conn) . "\n");
}

echo "\n5. ESPORTAZIONE STORIA_STUDENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_storia_studenti = "
COPY (
    SELECT *
    FROM storia_studenti
    WHERE flag_canc = 0
) TO '{$export_dir}storia_studenti_export.copy' WITH (FORMAT CSV, NULL '', DELIMITER E'\\t')
";

if (pg_query($conn, $query_storia_studenti)) {
    echo "✓ Storia studenti esportata in storia_studenti_export.copy\n";
} else {
    die("✗ Errore esportazione storia_studenti: " . pg_last_error($conn) . "\n");
}

echo "\n6. ESPORTAZIONE CLASSI_STUDENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_classi_studenti = "
COPY (
    SELECT *
    FROM classi_studenti
    WHERE flag_canc = 0
) TO '{$export_dir}classi_studenti_export.copy' WITH (FORMAT CSV, NULL '', DELIMITER E'\\t')
";

if (pg_query($conn, $query_classi_studenti)) {
    echo "✓ Classi studenti esportate in classi_studenti_export.copy\n";
} else {
    die("✗ Errore esportazione classi_studenti: " . pg_last_error($conn) . "\n");
}

echo "\n7. ESPORTAZIONE MAPPING CLASSI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_classi_mapping = "
COPY (
    SELECT id_classe, classe, sezione
    FROM classi
    WHERE flag_canc = 0
) TO '{$export_dir}classi_mapping_export.copy' WITH (FORMAT CSV, NULL '', DELIMITER E'\\t')
";

if (pg_query($conn, $query_classi_mapping)) {
    echo "✓ Mapping classi esportato in classi_mapping_export.copy\n";
} else {
    die("✗ Errore esportazione mapping classi: " . pg_last_error($conn) . "\n");
}

pg_close($conn);

echo "\n=== ESPORTAZIONE COMPLETATA ===\n";
echo "\nFile generati in {$export_dir}:\n";
echo "- studenti_export.copy\n";
echo "- parenti_export.copy\n";
echo "- parenti_studenti_export.copy\n";
echo "- storia_studenti_export.copy\n";
echo "- classi_studenti_export.copy\n";
echo "- classi_mapping_export.copy\n";

echo "\n=== PROSSIMI PASSI ===\n";
echo "1. Copia tutti i file .copy dal server B al server A nella directory /tmp/\n";
echo "2. Esegui lo script di importazione sul server A\n";
echo "3. Verifica l'integrità dei dati importati\n";

echo "\n=== COMANDI PER COPIARE I FILE ===\n";
echo "Sul tuo Mac, esegui:\n";
echo "# Scarica dal server B\n";
echo "scp user@server_b:{$export_dir}*_export.copy ~/Downloads/\n";
echo "\n# Carica sul server A\n";
echo "scp ~/Downloads/*_export.copy user@server_a:/tmp/\n";

?>
