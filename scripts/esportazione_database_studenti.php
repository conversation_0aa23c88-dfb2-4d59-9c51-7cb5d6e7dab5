#!/usr/bin/php
<?php
/**
 * Script per ESPORTAZIONE studenti, parenti e relazioni dal database di origine
 * 
 * PARTE 1: Da eseguire sul SERVER B (origine)
 * 
 * Esporta dal database B i seguenti dati in file .copy:
 * - studenti
 * - parenti
 * - parenti_studenti
 * - storia_studenti
 * - classi_studenti
 * - classi (per mapping)
 * 
 * I file generati dovranno essere copiati sul server A per l'importazione
 */

require __DIR__ . '/../configs/mastercom.php';
require MC_PATH . '/functions/common.php';
require MC_PATH . '/common/dbconnect.php';

// Configurazione database locale (SERVER B - origine)
$config = [
    'db_b' => [
        'name' => 'mastercom_2025_2026',
        'host' => 'localhost',
        'port' => 5432,
        'user' => 'postgres',
        'password' => 'postgres'
    ]
];

// Directory di esportazione
$export_dir = '/tmp/';

// Connessione database locale
$conn = pg_connect("host={$config['db_b']['host']} port={$config['db_b']['port']} dbname={$config['db_b']['name']} user={$config['db_b']['user']} password={$config['db_b']['password']}");

if (!$conn) {
    die("Errore di connessione al database locale\n");
}

echo "=== ESPORTAZIONE DATI DAL SERVER B ===\n";

// Statistiche pre-esportazione
echo "\n1. STATISTICHE PRE-ESPORTAZIONE\n";
echo "=" . str_repeat("=", 50) . "\n";

$tables_stats = [
    'studenti' => 'SELECT COUNT(*) FROM studenti WHERE flag_canc = 0',
    'parenti' => 'SELECT COUNT(*) FROM parenti WHERE flag_canc = 0',
    'parenti_studenti' => 'SELECT COUNT(*) FROM parenti_studenti WHERE flag_canc = 0',
    'storia_studenti' => 'SELECT COUNT(*) FROM storia_studenti WHERE flag_canc = 0',
    'classi_studenti' => 'SELECT COUNT(*) FROM classi_studenti WHERE flag_canc = 0',
    'classi' => 'SELECT COUNT(*) FROM classi WHERE flag_canc = 0'
];

foreach ($tables_stats as $table => $query) {
    $result = pg_query($conn, $query);
    $count = pg_fetch_result($result, 0, 0);
    echo "- $table: $count record\n";
}

echo "\n2. ESPORTAZIONE STUDENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_studenti = "
COPY (
    SELECT id_studente, nome, cognome, indirizzo, citta, cap, provincia, 
           sesso, telefono, cellulare1, cellulare2, email1, email2, invio_email, 
           invio_email_cumulativo, invio_email_parametrico, invio_email_temporale, 
           tipo_sms, tipo_sms_cumulativo, tipo_sms_parametrico, tipo_sms_temporale, 
           aut_entrata_ritardo, aut_uscita_anticipo, aut_pomeriggio, acconsente, 
           ritirato, data_nascita, codice_studente, password_studente, codice_giustificazioni_studente, 
           esonero_religione, materia_sostitutiva_religione, esonero_ed_fisica, 
           materia_sostitutiva_edfisica, crediti_terza, media_voti_terza, 
           debiti_terza, crediti_sospesi_terza, crediti_reintegrati_terza, 
           crediti_quarta, media_voti_quarta, debiti_quarta, crediti_sospesi_quarta, 
           crediti_reintegrati_quarta, crediti_quinta, media_voti_quinta, 
           crediti_finali_agg, matricola, luogo_nascita, provincia_nascita, 
           motivi_crediti_terza, motivi_crediti_quarta, motivi_crediti_quinta, 
           motivi_crediti_agg, codice_comune_nascita, stato_nascita, cittadinanza, 
           seconda_cittadinanza, codice_comune_residenza, distretto, codice_fiscale, 
           medico, telefono_medico, intolleranze_alim, gruppo_sanguigno, 
           gruppo_rh, codice_asl, annotazioni, stato_civile, voto_primo_scritto, 
           voto_secondo_scritto, voto_terzo_scritto, voto_orale, voto_bonus, 
           materia_secondo_scr, ulteriori_specif_diploma, numero_diploma, 
           chi_inserisce, data_inserimento, tipo_inserimento, chi_modifica, 
           data_modifica, tipo_modifica, flag_canc, stato_avanzamento, data_stato_avanzamento, 
           cap_provincia_nascita, badge, cap_residenza, codice_comune_domicilio, 
           cap_domicilio, cap_nascita, indirizzo_domicilio, citta_nascita_straniera, 
           cellulare_allievo, handicap, stato_convittore, data_ritiro, voto_ammissione, 
           differenza_punteggio, voto_qualifica, voto_esame_sc1_qual, voto_esame_sc2_qual, 
           voto_esame_or_qual, stato_privatista, foto, rappresentante, obbligo_formativo, 
           id_lingua_1, id_lingua_2, id_lingua_3, id_lingua_4, id_lingua_5, 
           id_provenienza_scolastica, id_scuola_media, lingua_scuola_media, 
           giudizio_scuola_media, trasporto, data_iscrizione, lingua_scuola_media_2, 
           pei, ammesso_esame_qualifica, ammesso_esame_quinta, giudizio_ammissione_quinta, 
           grado_handicap, tipo_handicap, stato_licenza_maestro, id_studente_sissi, 
           badge_rfid, lode, distretto_scolastico, giudizio_ammissione_terza, 
           esito_prima_media, esito_seconda_media, esito_terza_media, giudizio_esame_sc1_qual, 
           giudizio_esame_sc2_qual, giudizio_esame_or_qual, giudizio_complessivo_esame_qual, 
           curriculum_prima, curriculum_seconda, stage_professionali, acconsente_aziende, 
           data_orale, tipo_primo_scritto, tipo_secondo_scritto, tipo_terzo_scritto, 
           unanimita_primo_scritto, unanimita_secondo_scritto, unanimita_terzo_scritto, 
           argomento_scelto_orale, area_disc_1_orale, area_disc_2_orale, 
           disc_elaborati_orale, unanimita_voto_finale, ordine_esame_orale, 
           presente_esame_quinta, stampa_badge, id_classe_destinazione, 
           sconto_rette, carta_studente_numero, carta_studente_scadenza, 
           esito_corrente_calcolato, id_flusso, data_aggiornamento_sogei, 
           codice_alunno_ministeriale, flag_cf_fittizio, flag_s2f, codice_stato_sogei, 
           codice_gruppo_nomade, flag_minore_straniero, chiave, data_arrivo_in_italia, 
           frequenza_asilo_nido, frequenza_scuola_materna, voto_esame_medie_italiano, 
           voto_esame_medie_inglese, voto_esame_medie_matematica, voto_esame_medie_seconda_lingua, 
           voto_esame_medie_invalsi_ita, voto_esame_medie_invalsi_mat, voto_esame_medie_orale, 
           voto_ammissione_medie, esito_prima_elementare, esito_seconda_elementare, 
           esito_terza_elementare, esito_quarta_elementare, esito_quinta_elementare, 
           tipo_voto_esame_medie_italiano, tipo_voto_esame_medie_inglese, 
           giudizio_1_medie, giudizio_2_medie, giudizio_3_medie, argomenti_orali_medie, 
           giudizio_finale_1_medie, giudizio_finale_2_medie, giudizio_finale_3_medie, 
           consiglio_terza_media, giudizio_sintetico_esame_terza_media, 
           data_aggiornamento_sidi, cmp_sup_val_ita, cmp_sup_txt_ita, cmp_sup_val_ing, 
           cmp_sup_txt_ing, cmp_sup_val_altri, cmp_sup_txt_altri, cmp_sup_val_mat, 
           cmp_sup_txt_mat, cmp_sup_val_sci_tec, cmp_sup_txt_sci_tec, cmp_sup_val_sto_soc, 
           cmp_sup_txt_sto_soc, cmp_med_val_ita, cmp_med_txt_ita, cmp_med_val_ing, 
           cmp_med_txt_ing, cmp_med_val_altri, cmp_med_txt_altri, cmp_med_val_mat, 
           cmp_med_txt_mat, cmp_med_val_sci_tec, cmp_med_txt_sci_tec, cmp_med_val_sto_soc, 
           cmp_med_txt_sto_soc, cmp_med_val_l2, cmp_med_txt_l2, cmp_med_val_l3, 
           cmp_med_txt_l3, cmp_med_val_arte, cmp_med_txt_arte, cmp_med_val_mus, 
           cmp_med_txt_mus, cmp_med_val_mot, cmp_med_txt_mot, lingua_interfaccia, 
           password_modificata, id_piano_studio, necessita_alfabetizzazione, 
           qualifica_iefp, articolo_pei, numero_quesiti_esame_medie_matematica, 
           tipo_voto_esame_medie_seconda_lingua, voto_esame_medie_invalsi_finale, 
           servizio_mensa, bes, email_confermata, adottato, giudizio_prove_scritte_scuole_medie, id_quadro_orario_sidi,
           seconda_materia_sostitutiva_religione, vaccinazioni, necessita_sostegno, moduli_corsi_richiesti, 
           autocertificazione_vaccinazioni, consiglio_orientativo_trentino, esito_prima_superiore, esito_seconda_superiore, 
           esito_terza_superiore, esito_quarta_superiore, esito_quinta_superiore, sostenuto_prove_invalsi_medie,
           iban,fratelli,data_comunicazione_password,forza_competenze,stato_studente_personalizzato,codice_rid,tipo_addebito,
           rid,first_sepa,data_mandato_rid,attiva_modulo_preiscrizioni,num_volte_iscritto_classe_attuale,titolo_studio,data_credenziali_invalsi,
           aut_permesso_uscita,email_personale,annotazioni_amministrative,sicurezza_certificati,sicurezza_monteore,pdp,
           elaborato_studente_esame_terza_media,prova_esame_esame_terza_media,giudizio_descrittivo_finale_esame_terza_media,pasto_preferito,
           documento_tipo,documento_num,documento_data_inizio,documento_data_fine,documento_descrizione,permesso_soggiorno_num,permesso_soggiorno_data_inizio,permesso_soggiorno_data_fine,permesso_soggiorno_descrizione,
           id_software_origine,intestatario_fatture
    FROM studenti
    WHERE flag_canc = 0
) TO '{$export_dir}studenti_export.copy'
";

if (pg_query($conn, $query_studenti)) {
    echo "✓ Studenti esportati in studenti_export.copy\n";
} else {
    die("✗ Errore esportazione studenti: " . pg_last_error($conn) . "\n");
}

echo "\n3. ESPORTAZIONE PARENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_parenti = "
COPY (
    SELECT id_parente, cognome, nome, id_luogo_nascita, data_nascita, titolo_studio, 
           non_vivente, id_luogo_residenza, indirizzo, telefono_abitazione, 
           telefono_cellulare, codice_fiscale, email, id_stato_nascita, 
           id_stato_cittadinanza, sesso, id_stato_cittadinanza_2, privacy, 
           chi_inserisce, data_inserimento, tipo_inserimento, chi_modifica, 
           data_modifica, tipo_modifica, flag_canc, rappresentante, id_parente_sissi, 
           citta_straniera_nascita, cap_residenza, id_professione, id_titolo_studio, 
           utente, codice_attivazione, lingua_interfaccia, password, note,
           intestazione_cc, indirizzo_cc, data_comunicazione_password, id_famiglia, descrizione_professione,
           documento_tipo,documento_num,documento_data_inizio,documento_data_fine,documento_descrizione,id_software_origine
    FROM parenti
    WHERE flag_canc = 0
) TO '{$export_dir}parenti_export.copy'
";

if (pg_query($conn, $query_parenti)) {
    echo "✓ Parenti esportati in parenti_export.copy\n";
} else {
    die("✗ Errore esportazione parenti: " . pg_last_error($conn) . "\n");
}

echo "\n4. ESPORTAZIONE RELAZIONI PARENTI_STUDENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_parenti_studenti = "
COPY (
    SELECT id_studente, id_parente, parentela, patria_potesta, domiciliato_presso, 
           comunicazioni, flag_canc, chi_inserisce, data_inserimento, tipo_inserimento, 
           chi_modifica, data_modifica, tipo_modifica, rappresentante, pagante, sms_singoli,
           sms_gruppi, sms_temporali,intestatario_pagamenti, codice_rid ,tipo_addebito , rid, first_sepa, data_mandato_rid, iban , affidatario, ordinamento,iban_comunicato
    FROM parenti_studenti
    WHERE flag_canc = 0
) TO '{$export_dir}parenti_studenti_export.copy'
";

if (pg_query($conn, $query_parenti_studenti)) {
    echo "✓ Relazioni parenti_studenti esportate in parenti_studenti_export.copy\n";
} else {
    die("✗ Errore esportazione parenti_studenti: " . pg_last_error($conn) . "\n");
}

echo "\n5. ESPORTAZIONE STORIA_STUDENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_storia_studenti = "
COPY (
    SELECT id_storia_studenti, id_studente, classe, sezione, indirizzo, 
           altro, anno_scolastico, data_riferimento, chi_inserisce, data_inserimento, 
           tipo_inserimento, chi_modifica, data_modifica, tipo_modifica, 
           flag_canc, id_scuola, tipo_scuola, esito, descrizione, giudizio, 
           voto, media, tipo_studente, motivazione_interruzione,data_fine_frequenza_estero,
           stato_frequenza_estero,citta_frequenza_estero,scuola_frequenza_estero,id_classe,descrizione_libera_scuola
    FROM storia_studenti
    WHERE flag_canc = 0
) TO '{$export_dir}storia_studenti_export.copy'
";

if (pg_query($conn, $query_storia_studenti)) {
    echo "✓ Storia studenti esportata in storia_studenti_export.copy\n";
} else {
    die("✗ Errore esportazione storia_studenti: " . pg_last_error($conn) . "\n");
}

echo "\n6. ESPORTAZIONE CLASSI_STUDENTI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_classi_studenti = "
COPY (
    SELECT id_classe, id_studente, registro, chi_inserisce, data_inserimento, 
           tipo_inserimento, chi_modifica, data_modifica, tipo_modifica, 
           flag_canc, data_inizio_partecipazione, data_fine_partecipazione
    FROM classi_studenti
    WHERE flag_canc = 0
) TO '{$export_dir}classi_studenti_export.copy'
";

if (pg_query($conn, $query_classi_studenti)) {
    echo "✓ Classi studenti esportate in classi_studenti_export.copy\n";
} else {
    die("✗ Errore esportazione classi_studenti: " . pg_last_error($conn) . "\n");
}

echo "\n7. ESPORTAZIONE MAPPING CLASSI\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_classi_mapping = "
COPY (
    SELECT id_classe, classe, sezione
    FROM classi
    WHERE flag_canc = 0
) TO '{$export_dir}classi_mapping_export.copy'
";

if (pg_query($conn, $query_classi_mapping)) {
    echo "✓ Mapping classi esportato in classi_mapping_export.copy\n";
} else {
    die("✗ Errore esportazione mapping classi: " . pg_last_error($conn) . "\n");
}

pg_close($conn);

echo "\n=== ESPORTAZIONE COMPLETATA ===\n";
echo "\nFile generati in {$export_dir}:\n";
echo "- studenti_export.copy\n";
echo "- parenti_export.copy\n";
echo "- parenti_studenti_export.copy\n";
echo "- storia_studenti_export.copy\n";
echo "- classi_studenti_export.copy\n";
echo "- classi_mapping_export.copy\n";

echo "\n=== PROSSIMI PASSI ===\n";
echo "1. Copia tutti i file .copy dal server B al server A nella directory /tmp/\n";
echo "2. Esegui lo script di importazione sul server A\n";
echo "3. Verifica l'integrità dei dati importati\n";

echo "\n=== COMANDI PER COPIARE I FILE ===\n";
echo "Sul tuo Mac, esegui:\n";
echo "# Scarica dal server B\n";
echo "scp user@server_b:{$export_dir}*_export.copy ~/Downloads/\n";
echo "\n# Carica sul server A\n";
echo "scp ~/Downloads/*_export.copy user@server_a:/tmp/\n";

?>
