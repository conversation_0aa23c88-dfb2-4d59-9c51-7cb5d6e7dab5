#!/usr/bin/php
<?php
/**
 * Script per verificare l'integrità dei dati dopo l'importazione
 * 
 * Questo script esegue una serie di controlli per verificare che:
 * - I dati siano stati importati correttamente
 * - Non ci siano riferimenti orfani
 * - Le relazioni siano coerenti
 * - I conteggi corrispondano alle aspettative
 */

require __DIR__ . '/../configs/mastercom.php';
require MC_PATH . '/functions/common.php';
require MC_PATH . '/common/dbconnect.php';

// Configurazione database
$config = [
    'db_a' => [
        'name' => 'mastercom_2025_2026',
        'host' => 'localhost',
        'port' => 5432,
        'user' => 'postgres',
        'password' => 'postgres'
    ]
];

// Connessione database
$conn = pg_connect("host={$config['db_a']['host']} port={$config['db_a']['port']} dbname={$config['db_a']['name']} user={$config['db_a']['user']} password={$config['db_a']['password']}");

if (!$conn) {
    die("Errore di connessione al database\n");
}

$report_verifica = [
    'controlli_passati' => [],
    'controlli_falliti' => [],
    'warning' => [],
    'statistiche' => []
];

echo "=== VERIFICA INTEGRITÀ POST-IMPORTAZIONE ===\n\n";

// 1. CONTROLLO INTEGRITÀ REFERENZIALE
echo "1. CONTROLLO INTEGRITÀ REFERENZIALE\n";
echo "=" . str_repeat("=", 50) . "\n";

// Verifica parenti_studenti -> studenti
$query = "
    SELECT COUNT(*) as count 
    FROM parenti_studenti ps 
    LEFT JOIN studenti s ON ps.id_studente = s.id_studente 
    WHERE ps.flag_canc = 0 AND s.id_studente IS NULL
";
$result = pg_query($conn, $query);
$count = pg_fetch_result($result, 0, 0);

if ($count == 0) {
    echo "✓ Tutti i record parenti_studenti hanno studenti validi\n";
    $report_verifica['controlli_passati'][] = "Integrità parenti_studenti -> studenti";
} else {
    echo "✗ Trovati $count record parenti_studenti con studenti inesistenti\n";
    $report_verifica['controlli_falliti'][] = "Integrità parenti_studenti -> studenti: $count record orfani";
}

// Verifica parenti_studenti -> parenti
$query = "
    SELECT COUNT(*) as count 
    FROM parenti_studenti ps 
    LEFT JOIN parenti p ON ps.id_parente = p.id_parente 
    WHERE ps.flag_canc = 0 AND p.id_parente IS NULL
";
$result = pg_query($conn, $query);
$count = pg_fetch_result($result, 0, 0);

if ($count == 0) {
    echo "✓ Tutti i record parenti_studenti hanno parenti validi\n";
    $report_verifica['controlli_passati'][] = "Integrità parenti_studenti -> parenti";
} else {
    echo "✗ Trovati $count record parenti_studenti con parenti inesistenti\n";
    $report_verifica['controlli_falliti'][] = "Integrità parenti_studenti -> parenti: $count record orfani";
}

// Verifica classi_studenti -> studenti
$query = "
    SELECT COUNT(*) as count 
    FROM classi_studenti cs 
    LEFT JOIN studenti s ON cs.id_studente = s.id_studente 
    WHERE cs.flag_canc = 0 AND s.id_studente IS NULL
";
$result = pg_query($conn, $query);
$count = pg_fetch_result($result, 0, 0);

if ($count == 0) {
    echo "✓ Tutti i record classi_studenti hanno studenti validi\n";
    $report_verifica['controlli_passati'][] = "Integrità classi_studenti -> studenti";
} else {
    echo "✗ Trovati $count record classi_studenti con studenti inesistenti\n";
    $report_verifica['controlli_falliti'][] = "Integrità classi_studenti -> studenti: $count record orfani";
}

// Verifica classi_studenti -> classi
$query = "
    SELECT COUNT(*) as count 
    FROM classi_studenti cs 
    LEFT JOIN classi c ON cs.id_classe = c.id_classe 
    WHERE cs.flag_canc = 0 AND c.id_classe IS NULL
";
$result = pg_query($conn, $query);
$count = pg_fetch_result($result, 0, 0);

if ($count == 0) {
    echo "✓ Tutti i record classi_studenti hanno classi valide\n";
    $report_verifica['controlli_passati'][] = "Integrità classi_studenti -> classi";
} else {
    echo "✗ Trovati $count record classi_studenti con classi inesistenti\n";
    $report_verifica['controlli_falliti'][] = "Integrità classi_studenti -> classi: $count record orfani";
}

// Verifica storia_studenti -> studenti
$query = "
    SELECT COUNT(*) as count 
    FROM storia_studenti ss 
    LEFT JOIN studenti s ON ss.id_studente = s.id_studente 
    WHERE ss.flag_canc = 0 AND s.id_studente IS NULL
";
$result = pg_query($conn, $query);
$count = pg_fetch_result($result, 0, 0);

if ($count == 0) {
    echo "✓ Tutti i record storia_studenti hanno studenti validi\n";
    $report_verifica['controlli_passati'][] = "Integrità storia_studenti -> studenti";
} else {
    echo "✗ Trovati $count record storia_studenti con studenti inesistenti\n";
    $report_verifica['controlli_falliti'][] = "Integrità storia_studenti -> studenti: $count record orfani";
}

// 2. CONTROLLO DUPLICATI
echo "\n2. CONTROLLO DUPLICATI\n";
echo "=" . str_repeat("=", 50) . "\n";

// Duplicati studenti per codice fiscale
$query = "
    SELECT codice_fiscale, COUNT(*) as count 
    FROM studenti 
    WHERE flag_canc = 0 AND codice_fiscale != '' 
    GROUP BY codice_fiscale 
    HAVING COUNT(*) > 1
";
$result = pg_query($conn, $query);
$duplicati_studenti = pg_num_rows($result);

if ($duplicati_studenti == 0) {
    echo "✓ Nessun duplicato trovato per codice fiscale studenti\n";
    $report_verifica['controlli_passati'][] = "Nessun duplicato studenti per CF";
} else {
    echo "⚠ Trovati $duplicati_studenti codici fiscali duplicati per studenti\n";
    $report_verifica['warning'][] = "Duplicati studenti per CF: $duplicati_studenti";
    while ($row = pg_fetch_assoc($result)) {
        echo "  - CF: {$row['codice_fiscale']} ({$row['count']} occorrenze)\n";
    }
}

// Duplicati parenti per codice fiscale
$query = "
    SELECT codice_fiscale, COUNT(*) as count 
    FROM parenti 
    WHERE flag_canc = 0 AND codice_fiscale != '' 
    GROUP BY codice_fiscale 
    HAVING COUNT(*) > 1
";
$result = pg_query($conn, $query);
$duplicati_parenti = pg_num_rows($result);

if ($duplicati_parenti == 0) {
    echo "✓ Nessun duplicato trovato per codice fiscale parenti\n";
    $report_verifica['controlli_passati'][] = "Nessun duplicato parenti per CF";
} else {
    echo "⚠ Trovati $duplicati_parenti codici fiscali duplicati per parenti\n";
    $report_verifica['warning'][] = "Duplicati parenti per CF: $duplicati_parenti";
    while ($row = pg_fetch_assoc($result)) {
        echo "  - CF: {$row['codice_fiscale']} ({$row['count']} occorrenze)\n";
    }
}

// 3. STATISTICHE GENERALI
echo "\n3. STATISTICHE GENERALI\n";
echo "=" . str_repeat("=", 50) . "\n";

// Conteggio studenti
$query = "SELECT COUNT(*) as count FROM studenti WHERE flag_canc = 0";
$result = pg_query($conn, $query);
$count_studenti = pg_fetch_result($result, 0, 0);
echo "- Studenti attivi: $count_studenti\n";
$report_verifica['statistiche']['studenti_attivi'] = $count_studenti;

// Conteggio parenti
$query = "SELECT COUNT(*) as count FROM parenti WHERE flag_canc = 0";
$result = pg_query($conn, $query);
$count_parenti = pg_fetch_result($result, 0, 0);
echo "- Parenti attivi: $count_parenti\n";
$report_verifica['statistiche']['parenti_attivi'] = $count_parenti;

// Conteggio relazioni
$query = "SELECT COUNT(*) as count FROM parenti_studenti WHERE flag_canc = 0";
$result = pg_query($conn, $query);
$count_relazioni = pg_fetch_result($result, 0, 0);
echo "- Relazioni parenti_studenti: $count_relazioni\n";
$report_verifica['statistiche']['relazioni_parenti_studenti'] = $count_relazioni;

// Conteggio classi_studenti
$query = "SELECT COUNT(*) as count FROM classi_studenti WHERE flag_canc = 0";
$result = pg_query($conn, $query);
$count_classi_stud = pg_fetch_result($result, 0, 0);
echo "- Iscrizioni classi_studenti: $count_classi_stud\n";
$report_verifica['statistiche']['iscrizioni_classi'] = $count_classi_stud;

// Conteggio storia_studenti
$query = "SELECT COUNT(*) as count FROM storia_studenti WHERE flag_canc = 0";
$result = pg_query($conn, $query);
$count_storia = pg_fetch_result($result, 0, 0);
echo "- Record storia_studenti: $count_storia\n";
$report_verifica['statistiche']['record_storia'] = $count_storia;

// 4. CONTROLLI LOGICI
echo "\n4. CONTROLLI LOGICI\n";
echo "=" . str_repeat("=", 50) . "\n";

// Studenti senza classe
$query = "
    SELECT COUNT(*) as count 
    FROM studenti s 
    LEFT JOIN classi_studenti cs ON s.id_studente = cs.id_studente AND cs.flag_canc = 0
    WHERE s.flag_canc = 0 AND cs.id_studente IS NULL
";
$result = pg_query($conn, $query);
$studenti_senza_classe = pg_fetch_result($result, 0, 0);

if ($studenti_senza_classe == 0) {
    echo "✓ Tutti gli studenti sono assegnati a una classe\n";
    $report_verifica['controlli_passati'][] = "Tutti gli studenti hanno una classe";
} else {
    echo "⚠ Trovati $studenti_senza_classe studenti senza classe assegnata\n";
    $report_verifica['warning'][] = "Studenti senza classe: $studenti_senza_classe";
}

// Studenti senza parenti
$query = "
    SELECT COUNT(*) as count 
    FROM studenti s 
    LEFT JOIN parenti_studenti ps ON s.id_studente = ps.id_studente AND ps.flag_canc = 0
    WHERE s.flag_canc = 0 AND ps.id_studente IS NULL
";
$result = pg_query($conn, $query);
$studenti_senza_parenti = pg_fetch_result($result, 0, 0);

if ($studenti_senza_parenti == 0) {
    echo "✓ Tutti gli studenti hanno almeno un parente\n";
    $report_verifica['controlli_passati'][] = "Tutti gli studenti hanno parenti";
} else {
    echo "⚠ Trovati $studenti_senza_parenti studenti senza parenti\n";
    $report_verifica['warning'][] = "Studenti senza parenti: $studenti_senza_parenti";
}

pg_close($conn);

// 5. RIEPILOGO FINALE
echo "\n5. RIEPILOGO FINALE\n";
echo "=" . str_repeat("=", 50) . "\n";

echo "Controlli passati: " . count($report_verifica['controlli_passati']) . "\n";
echo "Controlli falliti: " . count($report_verifica['controlli_falliti']) . "\n";
echo "Warning: " . count($report_verifica['warning']) . "\n";

if (count($report_verifica['controlli_falliti']) == 0) {
    echo "\n✓ VERIFICA COMPLETATA CON SUCCESSO\n";
    echo "L'integrità dei dati è stata verificata.\n";
} else {
    echo "\n✗ VERIFICA COMPLETATA CON ERRORI\n";
    echo "Sono stati rilevati problemi di integrità che richiedono attenzione.\n";
}

// Salva report
$report_file = '/tmp/verifica_integrità_' . date('Y-m-d_H-i-s') . '.json';
file_put_contents($report_file, json_encode($report_verifica, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\nReport di verifica salvato in: $report_file\n";

echo "\n=== VERIFICA COMPLETATA ===\n";

?>
