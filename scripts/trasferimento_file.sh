#!/bin/bash

# Script per automatizzare il trasferimento dei file di esportazione
# tra SERVER B e SERVER A tramite il Mac locale

# Configurazione (MODIFICA QUESTI VALORI)
SERVER_B_HOST="<EMAIL>"
SERVER_A_HOST="<EMAIL>"
LOCAL_DIR="~/Downloads/mastercom_export"
REMOTE_DIR="/tmp"

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== SCRIPT TRASFERIMENTO FILE MASTERCOM ===${NC}"
echo ""

# Funzione per stampare messaggi colorati
print_step() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Verifica parametri
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Uso: $0 [download|upload|full]"
    echo ""
    echo "Comandi:"
    echo "  download  - Scarica file dal SERVER B al Mac"
    echo "  upload    - Carica file dal Mac al SERVER A"
    echo "  full      - Esegue download + upload completo"
    echo ""
    echo "Prima di usare lo script, modifica le configurazioni:"
    echo "  SERVER_B_HOST, SERVER_A_HOST, LOCAL_DIR"
    exit 0
fi

# Crea directory locale se non esiste
mkdir -p "$LOCAL_DIR"

# Lista dei file da trasferire
FILES=(
    "studenti_export.copy"
    "parenti_export.copy"
    "parenti_studenti_export.copy"
    "storia_studenti_export.copy"
    "classi_studenti_export.copy"
    "classi_mapping_export.copy"
)

# Funzione per scaricare dal SERVER B
download_from_server_b() {
    print_step "1. DOWNLOAD DAL SERVER B"
    echo "Scaricando file da $SERVER_B_HOST:$REMOTE_DIR"
    echo ""
    
    for file in "${FILES[@]}"; do
        echo -n "Scaricando $file... "
        if scp "$SERVER_B_HOST:$REMOTE_DIR/$file" "$LOCAL_DIR/" 2>/dev/null; then
            print_success "OK"
        else
            print_error "ERRORE"
            echo "Comando fallito: scp $SERVER_B_HOST:$REMOTE_DIR/$file $LOCAL_DIR/"
            return 1
        fi
    done
    
    echo ""
    print_success "Download completato!"
    
    # Verifica dimensioni file
    echo ""
    echo "Dimensioni file scaricati:"
    for file in "${FILES[@]}"; do
        if [ -f "$LOCAL_DIR/$file" ]; then
            size=$(ls -lh "$LOCAL_DIR/$file" | awk '{print $5}')
            echo "  $file: $size"
        else
            print_warning "File mancante: $file"
        fi
    done
}

# Funzione per caricare sul SERVER A
upload_to_server_a() {
    print_step "2. UPLOAD AL SERVER A"
    echo "Caricando file su $SERVER_A_HOST:$REMOTE_DIR"
    echo ""
    
    # Verifica che i file esistano localmente
    missing_files=()
    for file in "${FILES[@]}"; do
        if [ ! -f "$LOCAL_DIR/$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_error "File mancanti nella directory locale:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        echo ""
        echo "Esegui prima il download o verifica che l'esportazione sia stata completata."
        return 1
    fi
    
    for file in "${FILES[@]}"; do
        echo -n "Caricando $file... "
        if scp "$LOCAL_DIR/$file" "$SERVER_A_HOST:$REMOTE_DIR/" 2>/dev/null; then
            print_success "OK"
        else
            print_error "ERRORE"
            echo "Comando fallito: scp $LOCAL_DIR/$file $SERVER_A_HOST:$REMOTE_DIR/"
            return 1
        fi
    done
    
    echo ""
    print_success "Upload completato!"
}

# Funzione per verificare connessioni
verify_connections() {
    print_step "VERIFICA CONNESSIONI"
    
    echo -n "Testando connessione a SERVER B... "
    if ssh -o ConnectTimeout=5 -o BatchMode=yes "$SERVER_B_HOST" exit 2>/dev/null; then
        print_success "OK"
    else
        print_error "ERRORE"
        echo "Impossibile connettersi a $SERVER_B_HOST"
        echo "Verifica host, username e chiavi SSH"
        return 1
    fi
    
    echo -n "Testando connessione a SERVER A... "
    if ssh -o ConnectTimeout=5 -o BatchMode=yes "$SERVER_A_HOST" exit 2>/dev/null; then
        print_success "OK"
    else
        print_error "ERRORE"
        echo "Impossibile connettersi a $SERVER_A_HOST"
        echo "Verifica host, username e chiavi SSH"
        return 1
    fi
    
    echo ""
}

# Funzione principale
main() {
    case "$1" in
        "download")
            verify_connections || exit 1
            download_from_server_b
            ;;
        "upload")
            verify_connections || exit 1
            upload_to_server_a
            ;;
        "full"|"")
            verify_connections || exit 1
            download_from_server_b || exit 1
            echo ""
            upload_to_server_a || exit 1
            ;;
        *)
            print_error "Comando non riconosciuto: $1"
            echo "Usa: $0 [download|upload|full] o $0 --help"
            exit 1
            ;;
    esac
}

# Esegui funzione principale
main "$1"

echo ""
print_step "PROSSIMI PASSI"
echo "1. Verifica che tutti i file siano stati trasferiti correttamente"
echo "2. Sul SERVER A, esegui: php scripts/importazione_database_studenti.php"
echo "3. Verifica l'integrità: php scripts/verifica_integrità_post_importazione.php"
echo ""
print_success "Trasferimento completato!"

# Cleanup opzionale
read -p "Vuoi rimuovere i file locali? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f "$LOCAL_DIR"/*.copy
    print_success "File locali rimossi"
fi
