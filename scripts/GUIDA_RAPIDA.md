# Guida Rapida - Importazione Studenti Mastercom

## 📋 Checklist Completa

### ✅ Preparazione

- [ ] Backup del database di destinazione (SERVER A)
- [ ] Configurazione parametri di connessione negli script
- [ ] Verifica connessioni SSH tra i server
- [ ] Spazio disco sufficiente per i file di esportazione

### ✅ Esecuzione

- [ ] **PASSO 1**: Esportazione dal SERVER B
- [ ] **PASSO 2**: Trasferimento file
- [ ] **PASSO 3**: Importazione nel SERVER A
- [ ] **PASSO 4**: Verifica integrità
- [ ] **PASSO 5**: Controllo report

---

## 🚀 Comandi Rapidi

### SERVER B (Origine) - Esportazione

```bash
# 1. Configura il database nel file
nano scripts/esportazione_database_studenti.php

# 2. Esegui esportazione
php scripts/esportazione_database_studenti.php

# 3. Verifica file generati
ls -la /tmp/*_export.copy
```

### MAC (Trasferimento)

```bash
# Opzione 1: Script automatico (RACCOMANDATO)
# Configura prima SERVER_B_HOST e SERVER_A_HOST in:
nano scripts/trasferimento_file.sh

# Poi esegui:
./scripts/trasferimento_file.sh full

# Opzione 2: Manuale
scp user@server_b:/tmp/*_export.copy ~/Downloads/
scp ~/Downloads/*_export.copy user@server_a:/tmp/
```

### SERVER A (Destinazione) - Importazione

```bash
# 1. Backup database
pg_dump -h localhost -U postgres mastercom_2025_2026 > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. Configura il database nel file
nano scripts/importazione_database_studenti.php

# 3. Verifica file presenti
ls -la /tmp/*_export.copy

# 4. Esegui importazione
php scripts/importazione_database_studenti.php

# 5. Verifica integrità
php scripts/verifica_integrità_post_importazione.php
```

---

## 📊 File Generati

### Durante l'Esportazione (SERVER B)
```
/tmp/studenti_export.copy
/tmp/parenti_export.copy
/tmp/parenti_studenti_export.copy
/tmp/storia_studenti_export.copy
/tmp/classi_studenti_export.copy
/tmp/classi_mapping_export.copy
```

### Durante l'Importazione (SERVER A)
```
/tmp/report_importazione_YYYY-MM-DD_HH-mm-ss.json
/tmp/report_importazione_YYYY-MM-DD_HH-mm-ss.csv
/tmp/verifica_integrità_YYYY-MM-DD_HH-mm-ss.json
```

---

## ⚙️ Configurazioni Rapide

### Script di Esportazione
```php
// In esportazione_database_studenti.php
$config = [
    'db_b' => [
        'name' => 'mastercom_2025_2026',
        'host' => 'localhost',
        'port' => 5432,
        'user' => 'postgres',
        'password' => 'postgres'
    ]
];
```

### Script di Importazione
```php
// In importazione_database_studenti.php
$config = [
    'db_a' => [
        'name' => 'mastercom_2025_2026',
        'host' => 'localhost',
        'port' => 5432,
        'user' => 'postgres',
        'password' => 'postgres'
    ]
];
```

### Script di Trasferimento
```bash
# In trasferimento_file.sh
SERVER_B_HOST="user@server_b_ip"
SERVER_A_HOST="user@server_a_ip"
LOCAL_DIR="~/Downloads/mastercom_export"
```

---

## 🔍 Controlli di Verifica

### Prima dell'Importazione
```bash
# Verifica dimensioni file
ls -lh /tmp/*_export.copy

# Conta righe nei file
wc -l /tmp/*_export.copy

# Verifica connessione database
psql -h localhost -U postgres -d mastercom_2025_2026 -c "SELECT COUNT(*) FROM studenti;"
```

### Dopo l'Importazione
```bash
# Controlla report JSON
cat /tmp/report_importazione_*.json | jq '.errori | length'

# Verifica conteggi
psql -h localhost -U postgres -d mastercom_2025_2026 -c "
SELECT 
    'studenti' as tabella, COUNT(*) as record FROM studenti WHERE flag_canc = 0
UNION ALL
SELECT 
    'parenti' as tabella, COUNT(*) as record FROM parenti WHERE flag_canc = 0
UNION ALL
SELECT 
    'parenti_studenti' as tabella, COUNT(*) as record FROM parenti_studenti WHERE flag_canc = 0;
"
```

---

## 🚨 Risoluzione Problemi

### Errore: "File mancante"
```bash
# Verifica file sul server B
ssh user@server_b "ls -la /tmp/*_export.copy"

# Riesegui esportazione se necessario
ssh user@server_b "php /path/to/scripts/esportazione_database_studenti.php"
```

### Errore: "Connessione database"
```bash
# Testa connessione
psql -h localhost -U postgres -d mastercom_2025_2026 -c "SELECT version();"

# Verifica servizio PostgreSQL
sudo systemctl status postgresql
```

### Errore: "Duplicati codice fiscale"
```bash
# Controlla duplicati esistenti
psql -h localhost -U postgres -d mastercom_2025_2026 -c "
SELECT codice_fiscale, COUNT(*) 
FROM studenti 
WHERE flag_canc = 0 AND codice_fiscale != '' 
GROUP BY codice_fiscale 
HAVING COUNT(*) > 1;
"
```

### Errore: "Classe non trovata"
```bash
# Verifica mapping classi
psql -h localhost -U postgres -d mastercom_2025_2026 -c "
SELECT classe, sezione, COUNT(*) 
FROM classi 
WHERE flag_canc = 0 
GROUP BY classe, sezione 
HAVING COUNT(*) > 1;
"
```

---

## 📞 Supporto

### Log degli Errori
- Controlla sempre i file di report generati
- Salva l'output della console durante l'esecuzione
- Verifica i log di PostgreSQL in caso di errori SQL

### Rollback in Caso di Problemi
```bash
# Ripristina backup
psql -h localhost -U postgres -d mastercom_2025_2026 < backup_YYYYMMDD_HHMMSS.sql
```

### Contatti
- Controlla il README completo per dettagli aggiuntivi
- Usa lo script di analisi dipendenze per identificare tabelle aggiuntive
- Testa sempre su database di prova prima della produzione

---

## ⏱️ Tempi Stimati

- **Esportazione**: 5-15 minuti (dipende dalla dimensione del database)
- **Trasferimento**: 2-10 minuti (dipende dalla velocità di rete)
- **Importazione**: 10-30 minuti (dipende dal numero di record)
- **Verifica**: 2-5 minuti

**Tempo totale stimato**: 20-60 minuti
