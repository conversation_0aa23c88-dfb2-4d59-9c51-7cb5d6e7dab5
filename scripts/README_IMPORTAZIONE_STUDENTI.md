# Script di Importazione Studenti tra Database Mastercom

Questo pacchetto contiene gli script per importare studenti, parenti e relazioni tra due database Mastercom utilizzando un processo in due fasi: esportazione e importazione.

## File Inclusi

1. **esportazione_database_studenti.php** - Script di esportazione (da eseguire sul SERVER B)
2. **importazione_database_studenti.php** - Script di importazione (da eseguire sul SERVER A)
3. **analisi_dipendenze_importazione.php** - Script di analisi delle dipendenze
4. **verifica_integrità_post_importazione.php** - Script di verifica post-importazione
5. **README_IMPORTAZIONE_STUDENTI.md** - Questo file di documentazione

## Architettura del Processo

### FASE 1: Esportazione (SERVER B - Origine)
Lo script `esportazione_database_studenti.php` esporta i dati in file .copy:

- **studenti**: Tutti i dati degli studenti attivi
- **parenti**: Tutti i dati dei parenti attivi
- **parenti_studenti**: Relazioni tra parenti e studenti
- **storia_studenti**: Storico scolastico degli studenti
- **classi_studenti**: Iscrizioni alle classi
- **classi_mapping**: Mapping delle classi per la conversione ID

### FASE 2: Importazione (SERVER A - Destinazione)
Lo script `importazione_database_studenti.php` importa i dati dai file:

- **studenti**: Con nuovi id_studente generati automaticamente
- **parenti**: Con nuovi id_parente generati automaticamente
- **parenti_studenti**: Adeguato ai nuovi ID generati
- **storia_studenti**: Adeguato ai nuovi id_studente
- **classi_studenti**: Adeguato ai nuovi id_studente e mapping id_classe

### Gestione Duplicati

- **Studenti**: Se esiste già uno studente con lo stesso codice_fiscale in DB A, NON viene importato
- **Parenti**: Se esiste già un parente con lo stesso codice_fiscale in DB A, viene riutilizzato l'ID esistente

### Mapping Classi

Gli id_classe del DB B vengono sostituiti con quelli corrispondenti del DB A basandosi sui campi `classe` e `sezione` che devono essere univoci.

## Configurazione

### 1. Configurazione Script di Esportazione (SERVER B)

Apri `esportazione_database_studenti.php` e modifica la sezione di configurazione:

```php
$config = [
    'db_b' => [
        'name' => 'mastercom_2025_2026',        // Nome DB origine
        'host' => 'localhost',                   // Host locale
        'port' => 5432,                         // Porta DB
        'user' => 'postgres',                   // Username DB
        'password' => 'postgres'                // Password DB
    ]
];
```

### 2. Configurazione Script di Importazione (SERVER A)

Apri `importazione_database_studenti.php` e modifica la sezione di configurazione:

```php
$config = [
    'db_a' => [
        'name' => 'mastercom_2025_2026',        // Nome DB destinazione
        'host' => 'localhost',                   // Host locale
        'port' => 5432,                         // Porta DB
        'user' => 'postgres',                   // Username DB
        'password' => 'postgres'                // Password DB
    ]
];
```

### 3. Configurazione Script di Analisi

Apri `analisi_dipendenze_importazione.php` e modifica la configurazione del database da analizzare.

## Utilizzo Completo

### Passo 1: Analisi Preliminare (OPZIONALE)

Prima di iniziare, puoi eseguire l'analisi delle dipendenze su entrambi i server:

```bash
# Sul SERVER A e SERVER B
php scripts/analisi_dipendenze_importazione.php
```

### Passo 2: Backup del Database di Destinazione

**IMPORTANTE**: Esegui sempre un backup completo del database di destinazione:

```bash
# Sul SERVER A
pg_dump -h localhost -U postgres mastercom_2025_2026 > backup_pre_importazione.sql
```

### Passo 3: Esportazione dal SERVER B

```bash
# Sul SERVER B (origine)
php scripts/esportazione_database_studenti.php
```

Questo genererà i file in `/tmp/`:
- `studenti_export.copy`
- `parenti_export.copy`
- `parenti_studenti_export.copy`
- `storia_studenti_export.copy`
- `classi_studenti_export.copy`
- `classi_mapping_export.copy`

### Passo 4: Trasferimento File

Copia i file dal SERVER B al tuo Mac, poi dal Mac al SERVER A:

```bash
# Dal SERVER B al Mac
scp user@server_b:/tmp/*_export.copy ~/Downloads/

# Dal Mac al SERVER A
scp ~/Downloads/*_export.copy user@server_a:/tmp/
```

### Passo 5: Importazione nel SERVER A

```bash
# Sul SERVER A (destinazione)
php scripts/importazione_database_studenti.php
```

### Passo 6: Verifica Integrità

```bash
# Sul SERVER A
php scripts/verifica_integrità_post_importazione.php
```

### Passo 7: Controllo Report

Controlla i file di report generati:
- `/tmp/report_importazione_YYYY-MM-DD_HH-mm-ss.json` - Report dettagliato in JSON
- `/tmp/report_importazione_YYYY-MM-DD_HH-mm-ss.csv` - Report in formato CSV per Excel
- `/tmp/verifica_integrità_YYYY-MM-DD_HH-mm-ss.json` - Report di verifica integrità

## Output del Report

Il report include:

### Sezioni Principali
- **studenti_importati**: Lista studenti importati con successo
- **studenti_non_importati**: Lista studenti non importati (con motivo)
- **parenti_importati**: Lista parenti importati con successo
- **parenti_esistenti_riutilizzati**: Lista parenti già esistenti riutilizzati
- **relazioni_create**: Lista relazioni parenti_studenti create
- **storia_importata**: Lista record storia_studenti importati
- **classi_studenti_importate**: Lista record classi_studenti importati
- **errori**: Lista di tutti gli errori riscontrati

### Informazioni per Ogni Record
- ID originale e nuovo ID generato
- Nome, cognome, codice fiscale
- Motivo di eventuale esclusione
- Mapping degli ID tra database

## Controlli di Sicurezza

Lo script include diversi controlli:

1. **Verifica connessioni database** prima di iniziare
2. **Controllo duplicati** per codice fiscale
3. **Mapping sicuro** degli ID tra tabelle
4. **Gestione errori** con rollback automatico
5. **Report dettagliato** per verifiche post-importazione

## Tabelle Aggiuntive da Considerare

Dopo l'importazione principale, potresti dover importare anche:

- **voti** e valutazioni degli studenti
- **assenze** degli studenti
- **note_disciplinari**
- **comunicazioni** e messaggi
- **pagamenti** e situazione economica
- **documenti** allegati
- **foto** degli studenti

Usa lo script di analisi per identificare tutte le tabelle coinvolte.

## Risoluzione Problemi

### Errore di Connessione
- Verifica host, porta, username e password
- Controlla che i database esistano
- Verifica i permessi di accesso

### Errori di Mapping Classi
- Verifica che le classi esistano in entrambi i database
- Controlla che i campi classe+sezione siano univoci
- Verifica eventuali differenze nei nomi delle classi

### Errori di Duplicati
- Controlla i codici fiscali duplicati con le query nel report
- Valuta se pulire i dati prima dell'importazione

## Supporto

Per problemi o domande:
1. Controlla il file di log degli errori
2. Verifica il report JSON generato
3. Esegui le query di verifica manuale suggerite
4. Contatta il supporto tecnico con il report completo

## Note Importanti

- **Sempre fare backup** prima dell'importazione
- **Testare su database di prova** prima della produzione
- **Verificare i report** dopo ogni importazione
- **Considerare tabelle aggiuntive** in base all'analisi delle dipendenze
- **Pianificare downtime** per l'importazione in produzione
