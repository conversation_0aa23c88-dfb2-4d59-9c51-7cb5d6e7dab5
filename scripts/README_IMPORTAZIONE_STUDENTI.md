# Script di Importazione Studenti tra Database Mastercom

Questo pacchetto contiene gli script per importare studenti, parenti e relazioni tra due database Mastercom.

## File Inclusi

1. **importazione_database_studenti.php** - Script principale di importazione
2. **analisi_dipendenze_importazione.php** - Script di analisi delle dipendenze
3. **README_IMPORTAZIONE_STUDENTI.md** - Questo file di documentazione

## Funzionalità

### Script Principale (importazione_database_studenti.php)

Lo script importa da Database B verso Database A:

- **studenti**: Con nuovi id_studente generati automaticamente
- **parenti**: Con nuovi id_parente generati automaticamente  
- **parenti_studenti**: Adeguato ai nuovi ID generati
- **storia_studenti**: Adeguato ai nuovi id_studente
- **classi_studenti**: Adeguato ai nuovi id_studente e mapping id_classe

### Gestione Duplicati

- **Studenti**: Se esiste già uno studente con lo stesso codice_fiscale in DB A, NON viene importato
- **Parenti**: Se esiste già un parente con lo stesso codice_fiscale in DB A, viene riutilizzato l'ID esistente

### Mapping Classi

Gli id_classe del DB B vengono sostituiti con quelli corrispondenti del DB A basandosi sui campi `classe` e `sezione` che devono essere univoci.

## Configurazione

### 1. Modifica le Configurazioni Database

Apri `importazione_database_studenti.php` e modifica la sezione di configurazione:

```php
$config = [
    'db_a' => [
        'name' => 'mastercom_2025_2026',        // Nome DB destinazione
        'host' => 'localhost',                   // Host DB A
        'port' => 5432,                         // Porta DB A
        'user' => 'postgres',                   // Username DB A
        'password' => 'postgres'                // Password DB A
    ],
    'db_b' => [
        'name' => 'mastercom_2025_2026_b',      // Nome DB origine
        'host' => 'server_b_host',              // Host DB B
        'port' => 5432,                         // Porta DB B
        'user' => 'postgres',                   // Username DB B
        'password' => 'postgres'                // Password DB B
    ]
];
```

### 2. Modifica le Configurazioni per l'Analisi

Apri `analisi_dipendenze_importazione.php` e modifica la configurazione del database da analizzare.

## Utilizzo

### Passo 1: Analisi Preliminare

Prima di eseguire l'importazione, esegui l'analisi delle dipendenze:

```bash
php scripts/analisi_dipendenze_importazione.php
```

Questo script ti mostrerà:
- Tutte le tabelle che contengono id_studente, id_parente, id_classe
- Vincoli di chiave esterna
- Conteggio record nelle tabelle
- Raccomandazioni per tabelle aggiuntive da considerare

### Passo 2: Backup

**IMPORTANTE**: Esegui sempre un backup completo del database di destinazione prima dell'importazione:

```bash
pg_dump -h localhost -U postgres mastercom_2025_2026 > backup_pre_importazione.sql
```

### Passo 3: Esecuzione Importazione

```bash
php scripts/importazione_database_studenti.php
```

### Passo 4: Verifica Report

Lo script genera due file di report:
- `/tmp/report_importazione_YYYY-MM-DD_HH-mm-ss.json` - Report dettagliato in JSON
- `/tmp/report_importazione_YYYY-MM-DD_HH-mm-ss.csv` - Report in formato CSV per Excel

## Output del Report

Il report include:

### Sezioni Principali
- **studenti_importati**: Lista studenti importati con successo
- **studenti_non_importati**: Lista studenti non importati (con motivo)
- **parenti_importati**: Lista parenti importati con successo
- **parenti_esistenti_riutilizzati**: Lista parenti già esistenti riutilizzati
- **relazioni_create**: Lista relazioni parenti_studenti create
- **storia_importata**: Lista record storia_studenti importati
- **classi_studenti_importate**: Lista record classi_studenti importati
- **errori**: Lista di tutti gli errori riscontrati

### Informazioni per Ogni Record
- ID originale e nuovo ID generato
- Nome, cognome, codice fiscale
- Motivo di eventuale esclusione
- Mapping degli ID tra database

## Controlli di Sicurezza

Lo script include diversi controlli:

1. **Verifica connessioni database** prima di iniziare
2. **Controllo duplicati** per codice fiscale
3. **Mapping sicuro** degli ID tra tabelle
4. **Gestione errori** con rollback automatico
5. **Report dettagliato** per verifiche post-importazione

## Tabelle Aggiuntive da Considerare

Dopo l'importazione principale, potresti dover importare anche:

- **voti** e valutazioni degli studenti
- **assenze** degli studenti  
- **note_disciplinari**
- **comunicazioni** e messaggi
- **pagamenti** e situazione economica
- **documenti** allegati
- **foto** degli studenti

Usa lo script di analisi per identificare tutte le tabelle coinvolte.

## Risoluzione Problemi

### Errore di Connessione
- Verifica host, porta, username e password
- Controlla che i database esistano
- Verifica i permessi di accesso

### Errori di Mapping Classi
- Verifica che le classi esistano in entrambi i database
- Controlla che i campi classe+sezione siano univoci
- Verifica eventuali differenze nei nomi delle classi

### Errori di Duplicati
- Controlla i codici fiscali duplicati con le query nel report
- Valuta se pulire i dati prima dell'importazione

## Supporto

Per problemi o domande:
1. Controlla il file di log degli errori
2. Verifica il report JSON generato
3. Esegui le query di verifica manuale suggerite
4. Contatta il supporto tecnico con il report completo

## Note Importanti

- **Sempre fare backup** prima dell'importazione
- **Testare su database di prova** prima della produzione  
- **Verificare i report** dopo ogni importazione
- **Considerare tabelle aggiuntive** in base all'analisi delle dipendenze
- **Pianificare downtime** per l'importazione in produzione
